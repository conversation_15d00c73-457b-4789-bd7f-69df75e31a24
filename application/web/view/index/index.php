<!-- indexBanner -->
    <div class="indexBanner swiper-container">
        <div class="swiper-wrapper">
            <?php if (!count($banner)) { ?>
            
            <?php } else { foreach ($banner as $k => $v) { ?>
                <div class="swiper-slide">
                    <!-- <a href="javascript:;" style="background: url(<?=$dir_public . $v['logo']?>) top center no-repeat;"></a> -->
                    <a href="javascript:;"><img src="<?=$dir_public . $v['logo']?>" alt=""></a>
                </div>
            <?php } } ?>
        </div>
        <!-- Add Arrows -->
        <div class="swiper-button-next"></div>
        <div class="swiper-button-prev"></div>
    </div>

    <!-- indexNews -->
    <div class="indexNews">
        <div class="wrap">
            <div class="indexTitle between">
                <div class="title">新闻中心</div>
                <a href="<?=url('web/news/news')?>" class="more right">
                    <p>查看更多</p>
                    <div class="iconfont icon-arrow"></div>
                </a>
            </div>

            <div class="box between">
                <?php if (!count($news)) { ?>
                            
                <?php } else { foreach ($news as $k => $v) { if ($k < 1) { ?>
                    <a href="<?=url('web/news/detail', ['id' => $v['id']])?>" class="contBox">
                        <div class="img"><img src="<?=$dir_public . $v['logo']?>" alt=""></div>
                        <div class="cont">
                            <div class="time"><?=substr($v['release_time'], 0, 10)?></div>
                            <div class="tit cut"><?=$v['title']?></div>
                            <div class="txt cutTwo"><?=$v['desc']?></div>
                        </div>
                    </a>
                <?php } } } ?>

                <div class="newsBox swiper-container">
                    <div class="swiper-wrapper">
                        <?php if (!count($news)) { ?>
                                
                        <?php } else { foreach ($news as $k => $v) { if (1 <= $k && $k < 4) { ?>
                            <div class="swiper-slide">
                                <a href="<?=url('web/news/detail', ['id' => $v['id']])?>">
                                    <div class="time"><?=substr($v['release_time'], 0, 10)?></div>
                                    <div class="tit cutTwo"><?=$v['title']?></div>
                                    <div class="txt cutThree"><?=$v['desc']?></div>
                                </a>
                            </div>
                        <?php } } } ?>
                    </div>
                    <!-- Add Pagination -->
                    <div class="swiper-pagination"></div>
                </div>

                <ul>
                    <?php if (!count($news)) { ?>
                                
                    <?php } else { foreach ($news as $k => $v) { if ($k >= 4) { ?>
                        <li title="<?=$v['title']?>">
                            <a href="<?=url('web/news/detail', ['id' => $v['id']])?>" class="news-item">
                                <div class="news-content">
                                    <div class="news-header">
                                        <div class="news-title"><?=$v['title']?></div>
                                    </div>
                                    <div class="news-body"><?=$v['desc']?></div>
                                    <div class="news-date"><?=substr($v['release_time'], 0, 10)?></div>
                                </div>
                            </a>
                        </li>
                    <?php } } } ?>
                </ul>
            </div>
        </div>
    </div>

    <!-- indexSchedule -->
    <div class="indexSchedule" id="app">
        <div class="wrap">
            <div class="indexTitle between">
                <div class="title">论坛日程</div>
                <a href="<?=url('web/forum/forum')?>" class="more right">
                    <p>查看更多</p>
                    <div class="iconfont icon-arrow"></div>
                </a>
            </div>

            <ul class="timeBox centerT">
                <li :class="sele === 1 ? 'on' : ''" @click="changeTime('2024-09-07 00:00:00','2024-09-07 23:59:59', 1)">09-07</li>
                <li :class="sele === 2 ? 'on' : ''" @click="changeTime('2024-09-08 00:00:00','2024-09-08 23:59:59', 2)">09-08</li>
                <li :class="sele === 3 ? 'on' : ''" @click="changeTime('2024-09-09 00:00:00','2024-09-09 23:59:59', 3)">09-09</li>
            </ul>

            <div class="scheduleBox">
                <div v-for="(item,index) in lists" :key="index" @click="select(index)">
                    <div class="liBox" v-if="item.lt.FC1I3KSV08QWDZVDU">
                        <div class="Box between" :class="sel === index ? 'on' : ''">
                            <div class="titBox">
                                <div class="left">
                                    <div class="tit">{{item.lt.FC1I3KSV02JWDZVEX}}</div>
                                    <!-- <div class="label label1 centerT" v-if="item.lt.forumtime_type == 1">
                                        <div class="iconfont icon-zhibo"></div>
                                        <p>未开始</p>
                                    </div>
                                    <div class="label label2 centerT" v-if="item.lt.forumtime_type == 2">
                                        <div class="iconfont icon-zhibo"></div>
                                        <p>直播中</p>
                                    </div>
                                    <div class="label label3 centerT" v-if="item.lt.forumtime_type == 3">
                                        <div class="iconfont icon-zhibo"></div>
                                        <p>已结束</p>
                                    </div> -->
                                </div>
                                <div class="address left">
                                    <div class="left" style="margin-right: 20px;">
                                        <div class="iconfont icon-weizhi1"></div>
                                        <p>会议室：{{item.lt.FC1I3KSV03VWDZVUO_name}}</p>
                                    </div>
                                    <div class="left">
                                        <div class="iconfont icon-shijian"></div>
                                        <p>会议时间：{{item.lt.start_time}} - {{item.lt.end_time}}</p>
                                    </div>
                                </div>
                            </div>
                            <div class="btnBox right">
                                <!-- <a href="javascrupt:;" class="btn btn2" v-if="item.lt.forumtime_type == 1">敬请期待</a>
                                <a :href="item.lt.FC1I4JMDTNDWSUFPM" class="btn btn1" v-if="item.lt.forumtime_type == 2">去看直播</a>
                                <a :href="item.lt.FC1I4JMDTNDWSUFPM" class="btn btn1" v-if="item.lt.forumtime_type == 3">去看回放</a> -->
                                <div class="iconfont icon-jiantou"></div>
                            </div>
                        </div>
                        <div class="contBox" :class="sel === index ? 'sel' : ''">
                            <div class="cont">
                                <!-- <div class="t">论坛地点：中国•上海</div> -->
                                <div class="t" v-if="item.dw_data != ''">
                                    <div class="between" v-if="item.dw_data.a">
                                        <p>承办单位：</p>
                                        <p><span v-for="(it,inx) in item.dw_data.a" :key="inx">{{it.FC1I3KT05B6WDZVDO}}<em>、</em></span></p>
                                    </div>           
                                    <div class="between" v-if="item.dw_data.b">
                                        <p>协办单位：</p>
                                        <p><span v-for="(it,inx) in item.dw_data.b" :key="inx">{{it.FC1I3KT05B6WDZVDO}}<em>、</em></span></p>
                                    </div>        
                                    <div class="between" v-if="item.dw_data.c">
                                        <p>支持单位：</p>
                                        <p><span v-for="(it,inx) in item.dw_data.c" :key="inx">{{it.FC1I3KT05B6WDZVDO}}<em>、</em></span></p>
                                    </div>
                                    <!-- <p v-if="item.dw_data.a">承办单位：
                                        <span v-for="(it,inx) in item.dw_data.a" :key="inx">{{it.FC1I3KT05B6WDZVDO}}<em>、</em></span>
                                    </p>
                                    <p v-if="item.dw_data.b">协办单位：
                                        <span v-for="(it,inx) in item.dw_data.b" :key="inx">{{it.FC1I3KT05B6WDZVDO}}<em>、</em></span>
                                    </p>
                                    <p v-if="item.dw_data.c">支持单位：
                                        <span v-for="(it,inx) in item.dw_data.c" :key="inx">{{it.FC1I3KT05B6WDZVDO}}<em>、</em></span>
                                    </p> -->
                                </div>
                                <div class="t1" v-if="item.lt.FC1I3KSV03DWDZVUY">主题诠释</div>
                                <div class="t2">{{item.lt.FC1I3KSV03DWDZVUY}}</div>
                                <!-- <div class="show">
                                    <p>点击展开</p>
                                    <div class="iconfont icon-jiantou"></div>
                                </div> -->
                            </div>
                            <div class="dl" v-if="item.yc_row != ''">
                                <div v-for="(innerItem,innerIndex) in item.yc_row" :key="innerIndex">
                                    <div class="dt between" v-if="!innerItem.FC1I3KT0NT6WDZVMF">
                                        <div class="time">{{innerItem.duration_sd}}</div>
                                        <div class="dd">
                                            <div class="t3">{{innerItem.topicCn}}</div>
                                            <div class="left" v-if="innerItem.FC1I3KT0NQ3WDZVIB != 'a' && innerItem.FC1I3KT0NQ3WDZVIB != 'e'">
                                                <div class="li left" v-for="(inItem,inIndex) in innerItem.associatedAttendee" :key="inIndex">
                                                    <div class="img"><img :src="'https://fs.31huiyi.com/' + inItem.avatar" alt=""></div>
                                                    <div class="t4">
                                                        <div class="name">{{inItem.fullName}}</div>
                                                        <div class="t5">{{inItem.company}}</div>
                                                        <div class="t5">{{inItem.position}}</div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script>
        $(document).ready(function() {
            // 确保Vue库已加载
            if (typeof Vue !== 'undefined') {
                new Vue({
                    el: '#app',
                    data: {
                        start_time: '2024-09-07 00:00:00',
                        end_time: '2024-09-07 23:59:59',
                        lists: [],
                        sel: null, 
                        sele: 1,
                    },
                    created() {
                        this.forumList(this.start_time, this.end_time)
                    },
                    methods: {
                        changeTime(start, end, index) {
                            this.forumList(start, end)
                            this.sele = index
                            this.sel = null
                        },

                        forumList(start, end){
                            var that = this
                            axios({
                                method: 'post',
                                url: '<?=url('api/schedule/schedule_zs')?>',
                                data: {
                                    start_time: start,
                                    end_time: end,
                                }
                            }).then((res) => {
                                // console.log(res.data.data);
                                if (res.data.code == 200) {
                                    let lists = res.data.data
                                    lists.forEach((item, index) => {
                                        let now = new Date().getTime()
                                        let startTime = new Date(item.lt.forumtime[0]).getTime()
                                        let endTime = new Date(item.lt.forumtime[1]).getTime()
                                        item.lt.start_time = item.lt.forumtime[0].slice(5, 16)
                                        item.lt.end_time = item.lt.forumtime[1].slice(10, 16)
                                        if(now < startTime) {
                                            item.lt.forumtime_type = 1  // 未开始
                                        }
                                        if(now > startTime && now < endTime) {
                                            item.lt.forumtime_type = 2  // 进行中
                                        }
                                        if(now > endTime) {
                                            item.lt.forumtime_type = 3  // 已结束
                                        }
                                    })
                                    this.lists = lists
                                }
                            })
                        },

                        select(index) {
                            if (this.sel === index) {
                                this.sel = null; // 再次点击当前项时收起
                            } else {
                                this.sel = index; // 点击其他项时当前项展开，其他项收起
                            }
                            // this.sel = index
                        }
                    }
                })
            } else {
                console.warn('Vue library is not loaded');
            }
        });
    </script>

    <!-- indexReport -->
    <div class="indexReport">
        <div class="wrap">
            <ul class="tabList left">
                <li class="on">
                    <div class="p">浦江发布</div>
                    <a href="<?=url('web/news/news', ['pid' => 1, 'sort_id' => 4])?>" class="more">
                        <div class="right">
                            <p>查看更多</p>
                            <div class="iconfont icon-arrow"></div>
                        </div>
                    </a>
                </li>
                <li>
                    <div class="p">研究报告</div>
                    <a href="<?=url('web/review/report', ['id' => 1])?>" class="more">
                        <div class="right">
                            <p>查看更多</p>
                            <div class="iconfont icon-arrow"></div>
                        </div>
                    </a>
                </li>
            </ul>
        </div>

        <ul class="lists">
            <li>
                <div class="reportBox reportBox1 swiper-container">
                    <div class="swiper-wrapper">
                        <?php if (!count($release)) { ?>
                        
                        <?php } else { foreach ($release as $k => $v) { ?>
                            <div class="swiper-slide">
                                <a href="<?=url('web/news/detail', ['id' => $v['id']])?>">
                                    <div class="label">浦江发布</div>
                                    <div class="tit cut"><?=$v['title']?></div>
                                    <div class="txt cutTwo"><?=$v['desc']?></div>
                                    <div class="left more">
                                        <p>查看更多</p>
                                        <div class="iconfont icon-jiantou"></div>
                                    </div>
                                </a>
                            </div>
                        <?php } } ?>
                        <!-- <div class="swiper-slide">
                            <a href="#">
                                <div class="label">浦江发布</div>
                                <div class="tit cut">《上海科技金融生态年度观察2022》报告全文</div>
                                <div class="txt cutTwo">《上海科技金融生态年度观察2022》秉承开放协同的理念，由上海市科学学研究所、上海市科技创业中心…</div>
                                <div class="left more">
                                    <p>查看更多</p>
                                    <div class="iconfont icon-jiantou"></div>
                                </div>
                            </a>
                        </div> -->
                    </div>
                    <!-- Add Arrows -->
                    <div class="arrow">
                        <div class="swiper-button-next"></div>
                        <div class="swiper-button-prev"></div>
                    </div>
                </div>
            </li>
            <li style="display: none;">
                <div class="reportBox reportBox2 swiper-container">
                    <div class="swiper-wrapper">
                        <?php if (!count($report)) { ?>
                        
                        <?php } else { foreach ($report as $k => $v) { ?>
                            <div class="swiper-slide">
                                <a href="<?=$v['url']?>" download="<?=$v['url_name']?>">
                                    <div class="label">研究报告</div>
                                    <div class="tit cutTwo"><?=$v['title']?></div>
                                    <!-- <div class="txt cutTwo"><?=$v['title']?></div> -->
                                    <!-- <div class="left more">
                                        <p>查看更多</p>
                                        <div class="iconfont icon-jiantou"></div>
                                    </div> -->
                                    <div class="down">下载文件</div>
                                </a>
                            </div>
                        <?php } } ?>

                        <!-- <div class="swiper-slide">
                            <a href="#">
                                <div class="label">研究报告</div>
                                <div class="tit cut">《上海科技金融生态年度观察2022》报告全文</div>
                                <div class="txt cutTwo">《上海科技金融生态年度观察2022》秉承开放协同的理念，由上海市科学学研究所、上海市科技创业中心…</div>
                                <div class="left more">
                                    <p>查看更多</p>
                                    <div class="iconfont icon-jiantou"></div>
                                </div>
                            </a>
                        </div> -->
                    </div>
                    <!-- Add Arrows -->
                    <div class="arrow">
                        <div class="swiper-button-next"></div>
                        <div class="swiper-button-prev"></div>
                    </div>
                </div>
            </li>
        </ul>
    </div>
    <script>
        $(document).ready(function() {
            // 确保jQuery已加载
            if (typeof $ !== 'undefined') {
                $(".indexReport .tabList li").click(function(){
                    $(this).addClass('on').siblings().removeClass('on');
                    $(".indexReport .lists li").eq($(".indexReport .tabList li").index(this)).show().siblings().hide();
                });
            } else {
                console.warn('jQuery library is not loaded');
            }
        });
    </script>

    <!-- indexActivity -->
    <div class="indexActivity">
        <div class="wrap">
            <div class="indexTitle between">
                <div class="title">会议季活动</div>
                <a href="<?=url('web/news/news', ['pid' => 1, 'sort_id' => 5])?>" class="more right">
                    <p>查看更多</p>
                    <div class="iconfont icon-arrow"></div>
                </a>
            </div>

            <div class="activityBox swiper-container">
                <div class="swiper-wrapper">
                    <?php if (!count($activity)) { ?>

                    <?php } else { foreach ($activity as $k => $v) { ?>
                        <div class="swiper-slide">
                            <a href="<?=url('web/news/detail', ['id' => $v['id']])?>" class="activity-card">
                                <div class="card-bg" style="background-image: url('<?=$dir_public . $v['logo']?>');">
                                    <div class="card-overlay">
                                        <div class="card-content">
                                            <div class="label">会议季活动</div>
                                            <div class="tit"><?=$v['title']?></div>
                                        </div>
                                    </div>
                                </div>
                            </a>
                        </div>
                    <?php } } ?>

                    <!-- <div class="swiper-slide">
                        <a href="#" class="between">
                            <div class="img"><img src="<?=$dir_public?>/web/images/activityImg.jpg" alt=""></div>
                            <div class="cont">
                                <div class="label">会议季活动</div>
                                <div class="tit cutTwo">会议季│浦江创新论坛——2024科技创新智库国际研讨会开幕</div>
                                <div class="txt cutTwo">习近平指出，当前，世界百年未有之大变局加速演进，新一轮科技革命和产业变革深入发展…</div>
                                <div class="t left">
                                    <div class="iconfont icon-shijian"></div>
                                    <p>时间：2024.05.26-2024.05.30</p>
                                </div>
                                <div class="t left">
                                    <div class="iconfont icon-weizhi1"></div>
                                    <p>地点：中国•上海</p>
                                </div>
                            </div>
                        </a>
                    </div> -->
                </div>
                <!-- Add Pagination -->
                <div class="swiper-pagination"></div>
                <!-- Add Navigation -->
                <div class="swiper-button-next"></div>
                <div class="swiper-button-prev"></div>
            </div>
        </div>
    </div>

    <!-- indexAd -->
    <!-- <div class="indexAd">
        <div class="wrap">
            <div class="t1">共享创新 共塑未来</div>
            <div class="t2">构建科技创新开放环境</div>
        </div>
    </div> -->

    <!-- indexVideo -->
    <div class="indexVideo">
        <div class="wrap">
            <div class="indexTitle between">
                <div class="title">论坛视频</div>
                <ul class="tabList right">
                    <li class="on">平行未来的N次元</li>
                    <li>新华访谈</li>
                    <li>视频会客厅</li>
                </ul>
            </div>
        </div>

        <ul class="lists">
            <li>
                <div class="videoBox videoBox1 swiper-container">
                    <div class="swiper-wrapper">
                        <?php if (!count($videos)) { ?>
                        
                        <?php } else { foreach ($videos as $k => $v) { ?>
                            <div class="swiper-slide">
                                <a href="<?=url('web/news/detail', ['id' => $v['id']])?>">
                                    <div class="img">
                                        <img src="<?=$dir_public . $v['logo']?>" alt="">
                                        <div class="iconfont icon-bofang"></div>
                                    </div>
                                    <div class="cont">
                                        <div class="label">平行未来的N次元</div>
                                        <div class="tit cut"><?=$v['title']?></div>
                                        <div class="txt cut"><?=$v['desc']?></div>
                                    </div>
                                </a>
                            </div>
                        <?php } } ?>
                        <!-- <div class="swiper-slide">
                            <a href="#">
                                <div class="img">
                                    <img src="<?=$dir_public?>/web/images/videoImg.jpg" alt="">
                                    <div class="iconfont icon-bofang"></div>
                                </div>
                                <div class="cont">
                                    <div class="label">平行未来的N次元</div>
                                    <div class="tit cut">平行未来的N次元 | 跟随交大吕宝粮教授探寻“脑机结合”的未</div>
                                    <div class="txt cut">平行未来的N次元 | 跟随交大吕宝粮教授探寻"脑机结合"的未来</div>
                                </div>
                            </a>
                        </div> -->
                    </div>
                    <div class="arrowBox between wrap">
                        <div class="arrow">
                            <div class="swiper-button-next"></div>
                            <div class="swiper-button-prev"></div>
                        </div>
                        <a href="<?=url('web/news/news', ['pid' => 2, 'sort_id' => 8])?>" class="more right">
                            <p>查看更多</p>
                            <div class="iconfont icon-arrow"></div>
                        </a>
                    </div>
                </div>
            </li>
            <li style="display: none;">
                <div class="videoBox videoBox2 swiper-container">
                    <div class="swiper-wrapper">
                        <?php if (!count($interview)) { ?>
                        
                        <?php } else { foreach ($interview as $k => $v) { ?>
                            <div class="swiper-slide">
                                <a href="<?=url('web/news/detail', ['id' => $v['id']])?>">
                                    <div class="img">
                                        <img src="<?=$dir_public . $v['logo']?>" alt="">
                                        <div class="iconfont icon-bofang"></div>
                                    </div>
                                    <div class="cont">
                                        <div class="label">新华访谈</div>
                                        <div class="tit cut"><?=$v['title']?></div>
                                        <div class="txt cut"><?=$v['desc']?></div>
                                    </div>
                                </a>
                            </div>
                        <?php } } ?>
                        <!-- <div class="swiper-slide">
                            <a href="#">
                                <div class="img">
                                    <img src="<?=$dir_public?>/web/images/videoImg.jpg" alt="">
                                    <div class="iconfont icon-bofang"></div>
                                </div>
                                <div class="cont">
                                    <div class="label">视频会客厅</div>
                                    <div class="tit cut">平行未来的N次元 | 跟随交大吕宝粮教授探寻“脑机结合”的未</div>
                                    <div class="txt cut">平行未来的N次元 | 跟随交大吕宝粮教授探寻"脑机结合"的未来</div>
                                </div>
                            </a>
                        </div> -->
                    </div>
                    <div class="arrowBox between wrap">
                        <div class="arrow">
                            <div class="swiper-button-next"></div>
                            <div class="swiper-button-prev"></div>
                        </div>
                        <a href="<?=url('web/news/news', ['pid' => 2, 'sort_id' => 23])?>" class="more right">
                            <p>查看更多</p>
                            <div class="iconfont icon-arrow"></div>
                        </a>
                    </div>
                </div>
            </li>
            <li style="display: none;">
                <div class="videoBox videoBox3 swiper-container">
                    <div class="swiper-wrapper">
                        <?php if (!count($video)) { ?>
                        
                        <?php } else { foreach ($video as $k => $v) { ?>
                            <div class="swiper-slide">
                                <a href="<?=url('web/news/detail', ['id' => $v['id']])?>">
                                    <div class="img">
                                        <img src="<?=$dir_public . $v['logo']?>" alt="">
                                        <div class="iconfont icon-bofang"></div>
                                    </div>
                                    <div class="cont">
                                        <div class="label">视频会客厅</div>
                                        <div class="tit cut"><?=$v['title']?></div>
                                        <div class="txt cut"><?=$v['desc']?></div>
                                    </div>
                                </a>
                            </div>
                        <?php } } ?>
                        <!-- <div class="swiper-slide">
                            <a href="#">
                                <div class="img">
                                    <img src="<?=$dir_public?>/web/images/videoImg.jpg" alt="">
                                    <div class="iconfont icon-bofang"></div>
                                </div>
                                <div class="cont">
                                    <div class="label">视频会客厅</div>
                                    <div class="tit cut">平行未来的N次元 | 跟随交大吕宝粮教授探寻“脑机结合”的未</div>
                                    <div class="txt cut">平行未来的N次元 | 跟随交大吕宝粮教授探寻"脑机结合"的未来</div>
                                </div>
                            </a>
                        </div> -->
                    </div>
                    <div class="arrowBox between wrap">
                        <div class="arrow">
                            <div class="swiper-button-next"></div>
                            <div class="swiper-button-prev"></div>
                        </div>
                        <a href="<?=url('web/news/news', ['pid' => 2, 'sort_id' => 10])?>" class="more right">
                            <p>查看更多</p>
                            <div class="iconfont icon-arrow"></div>
                        </a>
                    </div>
                </div>
            </li>
        </ul>
    </div>
    <script>
        $(".indexVideo .tabList li").click(function(){
            $(this).addClass('on').siblings().removeClass('on')
            $(".indexVideo .lists li").eq($(".indexVideo .tabList li").index(this)).show().siblings().hide();
        });
    </script>

    <!-- indexPartner -->
    <div class="indexPartner">
        <div class="wrap">
            <?php if (!count($cooperatesort)) { ?>

            <?php } else { foreach ($cooperatesort as $k => $v) { ?>
                <div class="box between">
                    <div class="tit"><?=$v['name']?></div>
                    <ul class="left">
                        <?php if (!count($v['cooperate'])) { ?>

                        <?php } else { foreach ($v['cooperate'] as $kk => $vv) { ?>
                            <li>
                                <?php if ($vv['url']) { ?>
                                    <a href="<?=$vv['url']?>" target="_blank">
                                <?php } else { ?>
                                    <a href="javascrupt:;">
                                <?php } ?>
                                    <img src="<?=$dir_public . $vv['logo']?>" alt="">
                                </a>
                            </li>
                        <?php } } ?>
                    </ul>
                </div>
            <?php } } ?>

            <div class="box between">
                <div class="tit">合作媒体</div>
                <ul class="left">
                    <?php if (!count($medium)) { ?>

                    <?php } else { foreach ($medium as $k => $v) { ?>
                        <li>
                            <?php if ($v['url']) { ?>
                                <a href="<?=$v['url']?>" target="_blank">
                            <?php } else { ?>
                                <a href="javascrupt:;">
                            <?php } ?>
                                <img src="<?=$dir_public . $v['logo']?>" alt="">
                            </a>
                        </li>
                    <?php } } ?>
                </ul>
            </div>
        </div>
    </div>

    <script>
        $(document).ready(function() {
            // 确保Swiper库已加载
            if (typeof Swiper !== 'undefined') {
                var indexBanner = new Swiper('.indexBanner', {
                    // loop: true,
                    // autoplay: {
                    //     delay: 5000,
                    // },
                    navigation: {
                        nextEl: '.indexBanner .swiper-button-next',
                        prevEl: '.indexBanner .swiper-button-prev',
                    }
                });

                var newsBox = new Swiper('.newsBox', {
                    loop: true,
                    autoplay: {
                        delay: 5000,
                    },
                    pagination: {
                        el: '.swiper-pagination',
                        clickable :true,
                    }
                });

                var reportBox1 = new Swiper(".reportBox1", {
                    slidesPerView: "auto",
                    // centeredSlides: true,
                    spaceBetween: 36,
                    slidesPerView: 3,
                    observer: true, 
                    observeParents: true,
                    // loop: true,
                    // autoplay: {
                    //     delay: 5000,
                    // },
                    navigation: {
                        nextEl: '.reportBox1 .swiper-button-next',
                        prevEl: '.reportBox1 .swiper-button-prev',
                    }
                });

                var reportBox2 = new Swiper(".reportBox2", {
                    slidesPerView: "auto",
                    // centeredSlides: true,
                    spaceBetween: 36,
                    slidesPerView: 3,
                    observer: true, 
                    observeParents: true,
                    // loop: true,
                    // autoplay: {
                    //     delay: 5000,
                    // },
                    navigation: {
                        nextEl: '.reportBox2 .swiper-button-next',
                        prevEl: '.reportBox2 .swiper-button-prev',
                    }
                });

                var activityBox = new Swiper('.activityBox', {
                    watchSlidesProgress: true,
                    slidesPerView: 3,           // 可见3个元素
                    spaceBetween: 30,           // 间距20px
                    centeredSlides: true,       // 居中显示
                    loop: true,
                    loopedSlides: 5,
                    autoplay: {
                        delay: 4000,
                        disableOnInteraction: false,
                    },
                    navigation: {
                        nextEl: '.activityBox .swiper-button-next',
                        prevEl: '.activityBox .swiper-button-prev',
                    },
                    pagination: {
                        el: '.swiper-pagination',
                        clickable: true,
                        dynamicBullets: true,
                    },
                    on: {
                        progress: function(progress) {
                            for (i = 0; i < this.slides.length; i++) {
                                var slide = this.slides.eq(i);
                                var slideProgress = this.slides[i].progress;
                                modify = 1;
                                if (Math.abs(slideProgress) > 1) {
                                    modify = (Math.abs(slideProgress) - 1) * 0.3 + 1;
                                }
                                translate = slideProgress * modify * 200 + 'px';
                                scale = 1 - Math.abs(slideProgress) / 8;
                                zIndex = 999 - Math.abs(Math.round(10 * slideProgress));
                                slide.transform('translateX(' + translate + ') scale(' + scale + ')');
                                slide.css('zIndex', zIndex);
                                slide.css('opacity', 1);
                                if (Math.abs(slideProgress) > 2) {
                                    slide.css('opacity', 0.3);
                                }
                            }
                        },
                        setTransition: function(transition) {
                            for (var i = 0; i < this.slides.length; i++) {
                                var slide = this.slides.eq(i)
                                slide.transition(transition);
                            }
                        }
                    }
                })

                var videoBox = new Swiper(".videoBox", {
                    // slidesPerView: "auto",
                    // centeredSlides: true,
                    spaceBetween: 50,
                    slidesPerView: 2,
                    observer: true, 
                    observeParents: true,
                    // loop: true,
                    // autoplay: {
                    //     delay: 5000,
                    // },
                    navigation: {
                        nextEl: '.videoBox .swiper-button-next',
                        prevEl: '.videoBox .swiper-button-prev',
                    }
                });
            } else {
                console.warn('Swiper library is not loaded');
            }
        });
    </script>